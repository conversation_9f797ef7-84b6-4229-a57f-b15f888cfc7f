from flask import Flask, request, jsonify
from flask_cors import CORS # Import CORS
import os
import jsonify
import yaml
import requests
from io import StringIO, BytesIO
from atlassian import Jira
from jira import JIRAError
import json
app = Flask(__name__)
CORS(app) # Enable CORS for all routes (important for React frontend)
# Jira API Configuration
JIRA_URL = os.getenv("JIRA_URL", "https://jira-dc.paloaltonetworks.com/") # Replace with your Jira instance URL
JIRA_EMAIL = '<EMAIL>' # Replace with  Jira user
# JIRA_TOKEN = os.getenv("JIRA_TOKEN") # Replace with Jira API token

# Replace with your actual project key and issue type name
JIRA_PROJECT_KEY = "CRTX"
JIRA_ISSUE_TYPE_NAME = "Task"
# jira = jira_connect(jira_user=global_vars.JIRA_USER, jira_pass=global_vars.JIRA_PASS)

# Custom field IDs (replace with your actual custom field IDs from Jira)

@app.route('/health', methods=['GET'])
def health_check():
    return "200 OK"

@app.route('/process_data', methods=['POST'])
def process_data():
    if request.is_json:
        data = request.get_json()

        # Extract the fields
        metrics = data.get('metrics')
        env_prefix = data.get('qaTenant')
        lcaas_id = data.get('qaTenantLcaasId')

        # ---  Flask Logic Here ---
        

        # Trigger Cardinality script  
        print(f"Received metrics: {metrics}")
        print(f"Received ENV: {env_prefix}")
        print(f"Received LCAAS ID: {lcaas_id}")

        # Example: Return a success message and the received data
        return jsonify({
            "status": "success",
            "message": "Data received and processed successfully!",
            "received_data": {
                "metrics": metrics,
                "ENV_Prefix": env_prefix,
                "TenantLcaasId": lcaas_id
            }
        }), 200
    else:
        return jsonify({"status": "error", "message": "Request must be JSON"}), 400

@app.route('/create_jira_issue', methods=['POST'])
def create_jira_issue():
    JIRA_TOKEN = os.getenv("JIRA_TOKEN")  # REMOVE ME LATER
    if not request.is_json:
        return jsonify({"error": "Request must be JSON"}), 400
    data = request.get_json()
    jira_client = Jira(token=JIRA_TOKEN, url=JIRA_URL)

    # Extract data from the request body
    metrics_per_pod = data.get('metricsPerPod')
    cardinality_per_pod = data.get('cardinalityPerPod')
    rules = data.get('rules')
    metrics = data.get('metrics')
    alerts = data.get('alerts')
    project_id = data.get('project_id')
    env_prefix = data.get('env_prefix')
    target_branch = data.get('targetBranch')
    slack_links = data.get('slackLinks', [])
    summary = data.get('summary', 'New metric whitelist onboarding request') # Default summary
    description_prefix = f"{project_id} - Merics Onboarding Request created via Self Service Automation:\n\n"
    # print(metrics_per_pod)
    print(summary)
    # print(alerts)
    print(rules)
    # print(cardinality_per_pod)
    print(f'slack_links: {slack_links}')
    print(f'metrics: {metrics}')
    print(f'environment: {env_prefix}')
    print(f'target_branch: {target_branch}')
    requested_metrics = []
    for metric in metrics:
        requested_metrics.append(metric['name'])
    print(f'requested_metrics: {requested_metrics}')
    description_json = json.dumps(data)
    # print(description_json)

    # Construct the Jira issue payload
    issue_fields = {
        "project": {"key": "CRTX"},
        "assignee": {"name": "rnatour"},
        "components": [{"name": "DevOps SRE and Monitoring"}],
        "summary": summary,
        "issuetype": {"name": JIRA_ISSUE_TYPE_NAME},
        "description": (f"{description_prefix} \n "
                        f"Target Branch: {target_branch} \n "
                        f"Slack Cardinality Bot Links: {slack_links} \n "
                        f"Requested Metrics: {requested_metrics} \n "
                        f"Requested Rules: {rules}"),
        # "labels": ['label_one', 'another_label', 'python_automation']
    }
    # "description": f"This is a sample issue created via WhiltelistingSelf Service Automation. \n {metrics_per_pod} \n {cardinality_per_pod} \n {rules} \n {alerts}"
    # Add variables to the description or custom fields
    # if metrics_per_pod:
    #     issue_fields["description"].append({
    #         "type": "paragraph",
    #         "content": [
    #             {"type": "text", "text": f"Metrics and Cardinality per Pod:\n{metrics_per_pod}"}
    #         ]
    #     })
        # If you have a custom field for metrics, you can add it here:
        # issue_fields[CUSTOM_FIELD_METRICS] = metrics_per_pod
    
    # if cardinality_per_pod:
    #     # If you have a custom field for cardinality, you can add it here:
    #     # issue_fields[CUSTOM_FIELD_CARDINALITY] = cardinality_per_pod
    #     # Or just append to description if not using a separate custom field
    #     if not metrics_per_pod: # only append if not already appended with metrics_per_pod
    #         issue_fields["description"].append({
    #             "type": "paragraph",
    #             "content": [
    #                 {"type": "text", "text": f"Cardinality per Pod:\n{cardinality_per_pod}"}
    #             ]
    #         })

    # if rules:
    #     issue_fields["description"].append({
    #         "type": "paragraph",
    #         "content": [
    #             {"type": "text", "text": f"Rules Triggered:\n{rules}"}
    #         ]
    #     })
    #     # issue_fields[CUSTOM_FIELD_RULES] = rules

    # if alerts:
    #     issue_fields["description"].append({
    #         "type": "paragraph",
    #         "content": [
    #             {"type": "text", "text": f"Alerts Details:\n{alerts}"}
    #         ]
    #     })
        # issue_fields[CUSTOM_FIELD_ALERTS] = alerts

    jira_payload = {
        "fields": issue_fields, 
        "project": {"id": "CRTX"}
    }

    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "Authorization": f"Bearer {JIRA_TOKEN}"
    }
    # basic authentication is not supported by Jira Cloud, use token authentication instead
    # print(jira_payload)
    try:
        new_issue = jira_client.create_issue(fields=issue_fields)
        print("Issue created successfully!")
        print("Issue Key:", new_issue)
        print("Attaching Automation Files . . . . . ")
        
        issue_id_field = str(new_issue['key'])
        attachment_filename = f"{issue_id_field}-payload.json"

        # write content to a json
        # with open(attachment_filename, 'w') as f:
        #     json.dump({
        #         "project": project_id,
        #         "environment": env_prefix,
        #         "target_branch": target_branch,
        #         "rules": yaml.safe_load(rules), 
        #         "requested_metrics": yaml.safe_load(requested_metrics)
        #     }, f)







        with open(attachment_filename, 'w') as f:
            json.dump(data, f)

        jira_client.add_attachment(issue_key=issue_id_field, filename=attachment_filename)
        print(f"Automation File {attachment_filename} Attached to Jira {issue_id_field}!")
        # cleanup files
        os.remove(attachment_filename)
        return new_issue
    except JIRAError as e:
        print(f"JIRA Error creating Jira issue: {e.text}")
        return jsonify({"error": "Failed to create Jira issue", "details": e.text}), e.status_code


if __name__ == '__main__':
    # enable CORS
    from flask_cors import CORS
    CORS(app)
    app.run(host='0.0.0.0', debug=True, port=5001)