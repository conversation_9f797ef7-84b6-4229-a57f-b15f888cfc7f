import React, { useState, useEffect, useCallback } from 'react';
// ... (other imports remain the same)
import BranchTenantSelection from './components/BranchTenantSelection';
import MetricInput from './components/MetricInput';
import MetricListDisplay from './components/MetricListDisplay';
// import Cardinal<PERSON><PERSON>hecker from './components/CardinalityChecker';
import LabelSelection from './components/LabelSelection';
import RecordingRuleSelection from './components/RecordingRuleSelection';
import RecommendationEngine from './components/RecommendationEngine';
import JiraIntegration from './components/JiraIntegration';
import SlackBotLinkInput from './components/SlackBotLinkInput';
import SlackLinkListDisplay from './components/SlackLinkListDisplay';
import {
  MOCK_SOURCE_BRANCHES, MOCK_TARGET_BRANCHES, MOCK_QA_TENANTS, getAvailableLabelsForMetric
} from './utils/mockData';
import {
  runCardinalityAndEmissionCheck,
  generateRecordingRuleExample, // Generates initial rule { needed, example }
  generateAlertExamples,
  createJiraTicketApi
} from './utils/mockApi';


function Dashboard() {
  // ... (all existing state variables: sourceBranch, targetBranch, qaTenant, etc. remain the same)
  const [sourceBranch, setSourceBranch] = useState('');
  const [targetBranch, setTargetBranch] = useState('');
  const [qaTenant, setQaTenant] = useState('');
  const [qaTenantLcaasId, setQaTenantLcaasId] = useState('');
  const [slackLinks, setSlackLinks] = useState([]);

  // Set default values to first element in lists
  useEffect(() => {
    if (MOCK_TARGET_BRANCHES.length > 0 && !targetBranch) {
      setTargetBranch(MOCK_TARGET_BRANCHES[0]);
    }
  }, [targetBranch]);

  useEffect(() => {
    if (MOCK_QA_TENANTS.length > 0 && !qaTenant) {
      setQaTenant(MOCK_QA_TENANTS[0]);
    }
  }, [qaTenant]);

  useEffect(() => {
    if (!qaTenant) {
      setQaTenantLcaasId('');
    }
  }, [qaTenant]);

  const [metrics, setMetrics] = useState([]);
  const [metricIdCounter, setMetricIdCounter] = useState(0);

  const [cardinalityResults, setCardinalityResults] = useState({});
  const [isLoadingCardinality, setIsLoadingCardinality] = useState(false);

  const [selectedLabels, setSelectedLabels] = useState({});

  // Recording Rule Selection state
  const [recordingRuleSelections, setRecordingRuleSelections] = useState({}); // Structure: { metricId: boolean }
  const [recordingRuleRecommendations, setRecordingRuleRecommendations] = useState({}); // Structure: { metricId: { needed: boolean, example: string } }

  const [recordingRules, setRecordingRules] = useState({}); // Structure: { metricId: { needed: boolean, example: string (YAML or message) } }
  const [isLoadingRules, setIsLoadingRules] = useState(false);

  const [alertExamples, setAlertExamples] = useState({});
  const [isLoadingAlerts, setIsLoadingAlerts] = useState(false);

  const [jiraTicketInfo, setJiraTicketInfo] = useState(null);
  const [isCreatingJira, setIsCreatingJira] = useState(false);

  // ... (metric management functions: handleAddMetric, handleBatchAddMetrics, handleRemoveMetric remain the same)
  const handleAddMetric = useCallback((metricName) => {
    if (!metricName.trim()) return;

    setMetrics(prev => {
      const existingMetricNames = new Set(prev.map(m => m.name));
      const trimmedName = metricName.trim();

      // Only add if this metric name doesn't already exist
      if (!existingMetricNames.has(trimmedName)) {
        const newId = `metric-${metricIdCounter}`;
        setMetricIdCounter(prevCounter => prevCounter + 1);
        return [...prev, { id: newId, name: trimmedName }];
      }

      return prev; // Return unchanged if duplicate
    });
  }, [metricIdCounter]);

  const handleBatchAddMetrics = useCallback((batchText) => {
    const lines = batchText.split('\n').filter(line => line.trim() !== '');
    let currentIdCounter = metricIdCounter;

    setMetrics(prev => {
      const existingMetricNames = new Set(prev.map(m => m.name));
      const newMetrics = [];

      lines.forEach(line => {
        const parts = line.split(',').map(p => p.trim());
        const metricName = parts[0] || '';

        // Only add if this metric name doesn't already exist
        if (metricName && !existingMetricNames.has(metricName)) {
          const id = `metric-${currentIdCounter++}`;
          newMetrics.push({ id, name: metricName });
          existingMetricNames.add(metricName); // Track this new metric to avoid duplicates within the batch
        }
      });

      return [...prev, ...newMetrics];
    });

    setMetricIdCounter(currentIdCounter);
  }, [metricIdCounter]);

  const handleAddSlackLink = useCallback((link) => {
    if (!link) return;
    setSlackLinks(prev => [...prev, link.trim()]);
  }, []);

  const handleBatchAddSlackLinks = useCallback((batchText) => {
    const lines = batchText.split('\n').filter(line => line.trim() !== '');
    // Validation: one per line (already separated), no spaces/commas, valid URLs
    const cleaned = [];
    for (const l of lines) {
      const v = l.trim();
      if (!v) continue;
      if (/\s|,/.test(v)) continue;
      try {
        const u = new URL(v);
        if (u.protocol !== 'http:' && u.protocol !== 'https:') continue;
      } catch {
        continue;
      }
      cleaned.push(v);
    }
    if (cleaned.length > 0) {
      setSlackLinks(prev => [...prev, ...cleaned]);
    }
  }, []);

  const handleRemoveMetric = useCallback((metricIdToRemove) => {
    setMetrics(prev => prev.filter(m => m.id !== metricIdToRemove));
    setCardinalityResults(prev => { const newState = {...prev}; delete newState[metricIdToRemove]; return newState; });
    setSelectedLabels(prev => { const newState = {...prev}; delete newState[metricIdToRemove]; return newState; });
    setRecordingRules(prev => { const newState = {...prev}; delete newState[metricIdToRemove]; return newState; });
    setAlertExamples(prev => { const newState = {...prev}; delete newState[metricIdToRemove]; return newState; });

    // Clean up recording rule selections and recommendations
    setRecordingRuleSelections(prev => { const newState = {...prev}; delete newState[metricIdToRemove]; return newState; });
    setRecordingRuleRecommendations(prev => { const newState = {...prev}; delete newState[metricIdToRemove]; return newState; });
  }, []);

  const handleRemoveSlackLink = useCallback((indexToRemove) => {
    setSlackLinks(prev => prev.filter((_, idx) => idx !== indexToRemove));
  }, []);

  // ... (cardinality and labels functions: handleRunCardinalityChecks, handleLabelsChange remain the same)
  const handleRunCardinalityChecks = async () => {
    if (!qaTenant || metrics.length === 0 || !qaTenantLcaasId.trim()) {
      alert("To run cardinality checks, please select a QA tenant, provide its LCaaS ID, and add at least one metric.");
      return;
    }
    setIsLoadingCardinality(true);
    setCardinalityResults({});
    const results = await runCardinalityAndEmissionCheck(metrics, qaTenant, qaTenantLcaasId);
    setCardinalityResults(results);
    setIsLoadingCardinality(false);
  };

  const handleLabelsChange = (metricId, newLabels) => {
    setSelectedLabels(prev => ({ ...prev, [metricId]: newLabels }));
  };

  // Recording Rule Selection handlers
  const handleRecordingRuleToggle = useCallback((metricId, isSelected) => {
    setRecordingRuleSelections(prev => ({ ...prev, [metricId]: isSelected }));
  }, []);

  const handleRecordingRuleRecommendation = useCallback((metricId, recommendation) => {
    setRecordingRuleRecommendations(prev => ({ ...prev, [metricId]: recommendation }));
  }, []);

  const handleRecordingRuleEdit = useCallback((metricId, newRuleText) => {
    console.log('Dashboard: Recording rule edited for metric', metricId, 'New text:', newRuleText);
    setRecordingRuleRecommendations(prev => {
      const updated = {
        ...prev,
        [metricId]: {
          ...prev[metricId],
          example: newRuleText
        }
      };
      console.log('Dashboard: Updated recordingRuleRecommendations:', updated);
      return updated;
    });
  }, []);


  // --- Recommendations ---
  useEffect(() => { // Recording Rules (Initial Generation)
    const fetchRules = async () => {
       setIsLoadingRules(true);
      const newRules = {};
      for (const metric of metrics) {
        // Generate rules if labels are selected OR if cardinality has run for non-fatal metrics
        // This ensures we get the "not necessary" message if applicable.
        if ((selectedLabels[metric.id] || Object.keys(cardinalityResults).includes(metric.id))) {
          const ruleData = await generateRecordingRuleExample(metric.name, selectedLabels[metric.id] || []);
          newRules[metric.id] = ruleData; // ruleData should be { needed: boolean, example: string }
        }
      }
      setRecordingRules(newRules);
      setIsLoadingRules(false);
    };

    const shouldFetchRules = metrics.some(metric =>
        (selectedLabels[metric.id] || Object.keys(cardinalityResults).includes(metric.id))
    );

    if (metrics.length > 0 && (Object.keys(selectedLabels).length > 0 || Object.keys(cardinalityResults).length > 0) && shouldFetchRules) {
       fetchRules();
    } else if (metrics.length === 0) { // Clear rules if no metrics
        setRecordingRules({});
    }
  }, [selectedLabels, metrics, cardinalityResults]);


  useEffect(() => { // Alert Examples (Initial Generation)
    // ... (remains the same)
    const fetchAlerts = async () => {
      setIsLoadingAlerts(true);
      const newAlerts = {};
      for (const metric of metrics) {
        if (cardinalityResults[metric.id]?.status !== 'FatalX') {
          const alertData = await generateAlertExamples(metric.name, selectedLabels[metric.id], cardinalityResults[metric.id]);
          newAlerts[metric.id] = alertData;
        }
      }
      setAlertExamples(newAlerts);
      setIsLoadingAlerts(false);
    };
    if (metrics.length > 0 && Object.keys(cardinalityResults).length === metrics.length) {
      fetchAlerts();
    } else if (metrics.length === 0) { // Clear alerts if no metrics
        setAlertExamples({});
    }
  }, [metrics, cardinalityResults, selectedLabels]);

  // Handler for editable alert examples
  const handleAlertExampleChange = useCallback((metricId, newAlertYaml) => {
    // ... (remains the same)
    setAlertExamples(prevExamples => ({
      ...prevExamples,
      [metricId]: {
        ...(prevExamples[metricId] || {}),
        alerts: newAlertYaml,
      },
    }));
  }, []);

  // NEW: Handler for editable recording rule examples
  const handleRecordingRuleChange = useCallback((metricId, newRuleYaml) => {
    setRecordingRules(prevRules => ({
      ...prevRules,
      [metricId]: {
        ...(prevRules[metricId] || { needed: false }), // Preserve 'needed' or default if creating new
        example: newRuleYaml,
      },
    }));
  }, []);


  // --- Jira Integration ---
  const handleCreateJira = async () => {
    // ... (validation logic remains the same)
    if (!targetBranch || metrics.length === 0 || (qaTenant && !qaTenantLcaasId.trim()) ) {
        let alertMessage = "Please ensure Target Branch is selected and at least one metric is processed.";
        if (qaTenant && !qaTenantLcaasId.trim()) {
            alertMessage = "If an environment is selected, its LCaaS ID must also be provided for Jira creation.";
        }
        alert(alertMessage);
        return;
    }

    setIsCreatingJira(true);
    setJiraTicketInfo(null);

    const metricsSummaryTable = metrics.map(metric => {
        const cr = cardinalityResults[metric.id] || {};
        const labels = selectedLabels[metric.id] || [];
        const ruleInfo = recordingRules[metric.id];
        // Determine rule status for summary: "Yes (see example)", "No / Optional", or "Not Suggested"
        let ruleSummary = "Not Suggested";
        // if (ruleInfo) {
        //     if (ruleInfo.needed && ruleInfo.example && !ruleInfo.example.includes("not be necessary")) {
        //         ruleSummary = "Yes (see example)";
        //     } else if (ruleInfo.example && !ruleInfo.example.includes("not be necessary")) {
        //         ruleSummary = "Defined (see example)"; // If not "needed" but has YAML
        //     } else {
        //         ruleSummary = "No / Optional"; // If "not necessary" message or no YAML
        //     }
        // }

        return `| ${metric.name} | ${labels.join(', ')}`;
    }).join('\n');

    const jiraDescription = `
{"target_version":"${targetBranch}","environment": "${qaTenant}","project_id": "${qaTenant && qaTenantLcaasId }"}

${metrics.map(metric => {
    const cr = cardinalityResults[metric.id] || {};
    const alertInfo = alertExamples[metric.id];
    let details = `"Metric": "${metric.name}"`;

    // Cardinality Info
    // if (cr.message) {
    //     details += `* Cardinality/Emission on ${cr.checkedTenant || 'N/A'}${cr.checkedLcaasId ? ` (LCaaS ID: ${cr.checkedLcaasId})` : ''}: ${cr.status} - ${cr.message}\n`;
    // } else if (qaTenant) {
    //     details += `* Cardinality/Emission: Pending for selected QA Tenant.\n`;
    // }

    // Selected Labels
    // if (selectedLabels[metric.id]?.length > 0) {
    //     details += `* Selected Labels: ${selectedLabels[metric.id].join(', ')}\n`;
    // }

    // Recording Rule (only include if selected)
    const isRecordingRuleSelected = recordingRuleSelections[metric.id] === true;
    const recordingRuleRecommendation = recordingRuleRecommendations[metric.id];

    if (isRecordingRuleSelected && recordingRuleRecommendation?.example && recordingRuleRecommendation.example.trim() !== "") {
        details += `recording_rule_for : ${metric.name}\n${recordingRuleRecommendation.example.trim()}\n`;
    }


    // Alert Examples (already handles edited versions)
    if (alertInfo?.alerts && alertInfo.alerts.trim() !== "") {
        details += `* Alert Examples (Editable by Dev):\n`;
        if (alertInfo.message && alertInfo.message.trim() !== "" && !alertInfo.alerts.includes(alertInfo.message.trim())) {
            details += `{noformat}\n${alertInfo.message.trim()}\n{noformat}\n`;
        }
        details += `{code:yaml}\n${alertInfo.alerts.trim()}`;
    } else if (alertInfo?.message) {
        details += `* Alert Examples: ${alertInfo.message}\n`;
    }
    return details;
}).join('\n')}
`;

    const jiraPayload = {
      summary: `Metrics Onboarding Request for${qaTenant ? ` ${qaTenant + ' '}${qaTenantLcaasId}` : ''}`,
      description: jiraDescription,
    };

    // Prepare additional data for the API call
    // Only include recording rules that are actually selected
    console.log('Jira Creation: recordingRuleRecommendations:', recordingRuleRecommendations);
    console.log('Jira Creation: recordingRuleSelections:', recordingRuleSelections);

    const selectedRecordingRules = Object.entries(recordingRuleRecommendations)
      .filter(([metricId, _]) => {
        const isSelected = recordingRuleSelections[metricId] === true;
        console.log(`Metric ${metricId} selected:`, isSelected);
        return isSelected;
      })
      .map(([metricId, recommendation]) => {
        console.log(`Including rule for metric ${metricId}:`, recommendation.example);
        return recommendation.example;
      })
      .filter(rule => rule && rule.trim() !== "")
      .join('\n\n');

    console.log('Final selectedRecordingRules:', selectedRecordingRules);

    const additionalData = {
      lcaas : qaTenantLcaasId || '',
      project: qaTenantLcaasId || '',
      qaTenant: qaTenant || '',
      targetBranch: targetBranch || '',
      sourceBranch: sourceBranch || '',
      metrics: metrics,
      rules: selectedRecordingRules,
      alerts: Object.values(alertExamples).map(alert => alert.alerts).join('\n\n'),
      cardinality: cardinalityResults,
      slackLinks: slackLinks,
    };

    const response = await createJiraTicketApi(jiraPayload, additionalData);
    if (response.success) {
      setJiraTicketInfo(response.ticketInfo);
    } else {
      alert(`Jira creation failed: ${response.message}`);
    }
    setIsCreatingJira(false);
  };

  // ... (actionableMetrics, isJiraDisabled, and return JSX remain the same)
  // but ensure onRecordingRuleChange is passed to RecommendationEngine
  const actionableMetrics = metrics.filter(m => {
    if (!qaTenant && !cardinalityResults[m.id]) return true;
    return cardinalityResults[m.id]?.status !== 'FatalX';
  });

  const isJiraDisabled = metrics.length === 0 || !sourceBranch || !targetBranch || (qaTenant && !qaTenantLcaasId.trim());


  return (
    <div>
      <BranchTenantSelection
        sourceBranch={sourceBranch} setSourceBranch={setSourceBranch} sourceBranches={MOCK_SOURCE_BRANCHES}
        targetBranch={targetBranch} setTargetBranch={setTargetBranch} targetBranches={MOCK_TARGET_BRANCHES}
        qaTenant={qaTenant} setQaTenant={setQaTenant} qaTenants={MOCK_QA_TENANTS}
        qaTenantLcaasId={qaTenantLcaasId} setQaTenantLcaasId={setQaTenantLcaasId}
      />
      <hr />
      <MetricInput onAddMetric={handleAddMetric} onBatchAddMetrics={handleBatchAddMetrics} />
      <MetricListDisplay metrics={metrics} onRemoveMetric={handleRemoveMetric} />
      {/* <hr />
      <CardinalityChecker
        onRunChecks={handleRunCardinalityChecks}
        results={cardinalityResults}
        metrics={metrics}
        isLoading={isLoadingCardinality}
        qaTenant={qaTenant}
        qaTenantLcaasId={qaTenantLcaasId}
      /> */}
      <hr />
      {metrics.length > 0 && (
        <LabelSelection
          metrics={actionableMetrics}
          selectedLabels={selectedLabels}
          onLabelsChange={handleLabelsChange}
          getAvailableLabelsForMetric={getAvailableLabelsForMetric}
          cardinalityResults={cardinalityResults}
        />
      )}
      <hr />
      {metrics.length > 0 && (
        <RecordingRuleSelection
          metrics={actionableMetrics}
          selectedLabels={selectedLabels}
          cardinalityResults={cardinalityResults}
          onRecordingRuleToggle={handleRecordingRuleToggle}
          recordingRuleSelections={recordingRuleSelections}
          onRecordingRuleRecommendation={handleRecordingRuleRecommendation}
          onRecordingRuleEdit={handleRecordingRuleEdit}
        />
      )}
      {/* <hr />
       <RecommendationEngine
        metrics={metrics}
        recordingRules={recordingRules}
        onRecordingRuleChange={handleRecordingRuleChange} // <<< Pass new handler
        alertExamples={alertExamples}
        onAlertExampleChange={handleAlertExampleChange}
        isLoadingRules={isLoadingRules}
        isLoadingAlerts={isLoadingAlerts}
        cardinalityResults={cardinalityResults}
        selectedLabels={selectedLabels}
      /> */}
      <hr />
      <SlackBotLinkInput onAddLink={handleAddSlackLink} onBatchAddLinks={handleBatchAddSlackLinks} />
      <SlackLinkListDisplay links={slackLinks} onRemoveLink={handleRemoveSlackLink} />
      <hr />
      <JiraIntegration
        onCreateJira={handleCreateJira}
        isCreating={isCreatingJira}
        ticketInfo={jiraTicketInfo}
        disabled={isJiraDisabled}
        metrics={metrics}
      />
    </div>
  );
}
export default Dashboard;