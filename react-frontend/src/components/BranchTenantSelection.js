import React, { useState, useEffect } from 'react';

function BranchTenantSelection({
  sourceBranch, setSourceBranch, sourceBranches,
  targetBranch, setTargetBranch, targetBranches,
  qaTenant, setQaTenant, qaTenants,
  qaTenantLcaasId, setQaTenantLcaasId // <-- New props
}) {
  const [validationError, setValidationError] = useState('');

  // Re-validate when environment changes
  useEffect(() => {
    if (qaTenantLcaasId) {
      const error = validateProjectId(qaTenantLcaasId, qaTenant);
      setValidationError(error);
    }
  }, [qaTenant, qaTenantLcaasId]);

  // Validation function for Project ID based on environment
  const validateProjectId = (value, environment) => {
    if (!value) {
      return ''; // Allow empty value
    }

    // Define validation patterns for each environment
    const validationRules = {
      'ST MAIN': {
        patterns: [/^qa2-test-\d+$/, /^qa4-test-\d+$/],
        message: 'Project ID must start with "qa2-test-" or "qa4-test-" followed by a number (e.g., qa2-test-123, qa4-test-456)'
      },
      'ST Engine': {
        patterns: [/^engine-qa2-test-\d+$/, /^engine-qa4-test-\d+$/],
        message: 'Project ID must start with "engine-qa2-test-" or "engine-qa4-test-" followed by a number (e.g., engine-qa2-test-123, engine-qa4-test-456)'
      },
      'ST App Hub': {
        patterns: [/^app-hub-qa2-test-\d+$/, /^app-hub-qa4-test-\d+$/],
        message: 'Project ID must start with "app-hub-qa2-test-" or "app-hub-qa4-test-" followed by a number (e.g., app-hub-qa2-test-123, app-hub-qa4-test-456)'
      },
      'Multi-Tenant': {
        patterns: [/.*-dev-.*/],
        message: 'Project ID must contain "-dev-" for Multi-Tenant environment (e.g., project-dev-123)'
      }
    };

    const rule = validationRules[environment];
    if (!rule) {
      return ''; // No validation for unknown environments
    }

    const isValid = rule.patterns.some(pattern => pattern.test(value));
    if (!isValid) {
      return rule.message;
    }

    return '';
  };

  // Generate placeholder text based on environment
  const getPlaceholderText = (environment) => {
    const placeholders = {
      'ST MAIN': 'qa2-test-123 or qa4-test-456',
      'ST Engine': 'engine-qa2-test-123 or engine-qa4-test-456',
      'ST App Hub': 'app-hub-qa2-test-123 or app-hub-qa4-test-456',
      'Multi-Tenant': 'project-dev-123'
    };
    return placeholders[environment] || 'Enter Project ID';
  };

  // Handle Project ID change with validation
  const handleProjectIdChange = (e) => {
    const value = e.target.value;
    setQaTenantLcaasId(value);

    const error = validateProjectId(value, qaTenant);
    setValidationError(error);
  };
  return (
    <section className="dashboard-section">
      <h2>1. Select Version, Environment, and enter Project ID</h2>
      <div className="form-group">
        <label htmlFor="targetBranch">Target Branch:</label>
        <select id="targetBranch" value={targetBranch} onChange={(e) => setTargetBranch(e.target.value)}>
          <option value="">-- Select Target Branch --</option>
          {targetBranches.map(branch => <option key={branch} value={branch}>{branch}</option>)}
        </select>
      </div>
      <div className="form-group">
        <label htmlFor="qaTenant">Environment</label>
        <select id="qaTenant" value={qaTenant} onChange={(e) => setQaTenant(e.target.value)}>
          <option value="">-- Select Environment --</option>
          {qaTenants.map(tenant => <option key={tenant} value={tenant}>{tenant}</option>)}
        </select>
      </div>
      {/* New Input Field for Project ID */}
      <div className="form-group">
        <label htmlFor="qaTenantLcaasId">Project ID:</label>
        <input
          type="text"
          id="qaTenantLcaasId"
          value={qaTenantLcaasId}
          onChange={handleProjectIdChange}
          placeholder={getPlaceholderText(qaTenant)}
          className={validationError ? 'input-error' : ''}
        />
        {validationError && (
          <div className="error-message" style={{ color: 'red', fontSize: '0.875rem', marginTop: '4px' }}>
            {validationError}
          </div>
        )}
      </div>
    </section>
  );
}

export default BranchTenantSelection;