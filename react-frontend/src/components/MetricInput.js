import React, { useState, useMemo } from 'react';

function MetricInput({ onAddMetric, onBatchAddMetrics }) {
  const [metricName, setMetricName] = useState('');
  const [podName, setPodName] = useState('');
  const [batchInput, setBatchInput] = useState('');

  const isInvalidWildcardOrRegex = (s) => /[\*\?\[\]\(\)\|\+\^\$]/.test(s);
  const isLowerSnakeCase = (s) => /^[a-z0-9_]+$/.test(s) && s === s.toLowerCase();

  const singleError = (() => {
    const v = metricName.trim();
    if (!v) return '';
    if (isInvalidWildcardOrRegex(v)) return 'Wildcards or regex characters are not allowed (* ? [ ] ( ) | + ^ $).';
    if (!isLowerSnakeCase(v)) return 'Metric name must be lowercase letters/numbers and underscores only.';
    // No symbols except underscore enforced by regex above
    return '';
  })();

  const handleAdd = () => {
    if (!metricName.trim() || singleError) return;
    onAddMetric(metricName, podName);
    setMetricName('');
    setPodName('');
  };

  const handleBatch = () => {
    const raw = batchInput.trim();
    if (!raw) return;
    // For batch: enforce one per line, optional ",pod" component remains supported.
    // Validate each metric name before passing through.
    const lines = raw.split('\n').filter(Boolean);
    const invalids = [];
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const parts = line.split(',');
      const name = (parts[0] || '').trim();
      if (!name || isInvalidWildcardOrRegex(name) || !isLowerSnakeCase(name)) {
        invalids.push(i + 1);
      }
    }
    if (invalids.length > 0) {
      alert(`Invalid metric name(s) on line(s): ${invalids.join(', ')}.\nRules: no wildcards/regex; lowercase with snake_case; only letters, numbers, underscore.`);
      return;
    }
    onBatchAddMetrics(batchInput);
    setBatchInput('');
  };

  return (
    <section className="dashboard-section">
      <h2>2. Add Metrics</h2>
      <div className="form-group">
        <label htmlFor="metricName">Please provide specific names and not just whitelist prefix or suffix with wildcard. ​​Refer to {' '}
    <a href="https://confluence-dc.paloaltonetworks.com/pages/viewpage.action?spaceKey=VisibilityApplication&title=Metrics+best+practice" target="_blank" rel="noopener noreferrer">
      Metrics best practice
    </a>.
</label>
        <input
          type="text"
          id="metricName"
          value={metricName}
          onChange={(e) => setMetricName(e.target.value)}
          placeholder="e.g., http_requests_total"
        />
      </div>
      {singleError && (
        <p className="message message-warning" style={{ marginTop: '6px' }}>{singleError}</p>
      )}
      <button onClick={handleAdd} disabled={!metricName.trim() || !!singleError}>Add Metric Individually</button>

      <div className="form-group" style={{ marginTop: '20px' }}>
        <label htmlFor="batchInput">Add Metrics in Batch (one metric per line):</label>
        <textarea
          id="batchInput"
          value={batchInput}
          onChange={(e) => setBatchInput(e.target.value)}
          placeholder="metric_one&#10;metric_two&#10;metric_three"
        />
      </div>
      <button onClick={handleBatch} disabled={!batchInput.trim()}>Add Batch</button>
    </section>
  );
}

export default MetricInput;